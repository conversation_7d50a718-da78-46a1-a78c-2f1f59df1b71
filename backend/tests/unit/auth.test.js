/**
 * Unit tests for Auth Lambda function
 */

const { TestDataGenerator, TEST_USERS, TEST_CREDENTIALS } = require('../utils/test-data');
const { 
  resetAllMocks, 
  mockDynamoDBGet, 
  mockDynamoDBPut, 
  mockDynamoDBQuery,
  mockCognitoAdminCreateUser,
  mockCognitoAdminInitiateAuth
} = require('../utils/aws-mocks');

// Import the handler
const { handler } = require('../../src/auth/index');

describe('Auth Lambda Handler', () => {
  beforeEach(() => {
    resetAllMocks();
  });

  describe('POST /auth/signup', () => {
    it('should create a new user successfully', async () => {
      // Mock Cognito and DynamoDB responses
      mockCognitoAdminCreateUser(TestDataGenerator.createCognitoUser());
      mockDynamoDBPut();

      const signupData = {
        email: '<EMAIL>',
        password: 'Password123!',
        username: 'newuser',
        firstName: 'New',
        lastName: 'User'
      };

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/auth/signup',
        body: JSON.stringify(signupData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(201);
      const body = JSON.parse(result.body);
      expect(body.message).toBe('User created successfully');
      expect(body.user).toBeDefined();
      expect(body.user.email).toBe(signupData.email);
      expect(body.user.username).toBe(signupData.username);
      expect(body.user.firstName).toBe(signupData.firstName);
      expect(body.user.lastName).toBe(signupData.lastName);
    });

    it('should return 400 when required fields are missing', async () => {
      const testCases = [
        { email: '<EMAIL>', password: 'Password123!' }, // Missing username
        { email: '<EMAIL>', username: 'testuser' }, // Missing password
        { password: 'Password123!', username: 'testuser' }, // Missing email
        {} // All missing
      ];

      for (const testCase of testCases) {
        const event = TestDataGenerator.createAPIGatewayEvent({
          httpMethod: 'POST',
          path: '/auth/signup',
          body: JSON.stringify(testCase)
        });
        const context = TestDataGenerator.createLambdaContext();

        const result = await handler(event, context);

        expect(result.statusCode).toBe(400);
        const body = JSON.parse(result.body);
        expect(body.error).toBe('Email, password, and username are required');
      }
    });

    it('should handle Cognito errors during signup', async () => {
      // Mock Cognito error
      require('../utils/aws-mocks').mockCognitoIdentityServiceProvider.adminCreateUser.mockImplementation(() => ({
        promise: () => Promise.reject(new Error('User already exists'))
      }));

      const signupData = {
        email: '<EMAIL>',
        password: 'Password123!',
        username: 'existinguser',
        firstName: 'Existing',
        lastName: 'User'
      };

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/auth/signup',
        body: JSON.stringify(signupData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Failed to create user');
      expect(body.details).toBe('User already exists');
    });
  });

  describe('POST /auth/signin', () => {
    it('should sign in a user successfully', async () => {
      // Mock Cognito auth response
      mockCognitoAdminInitiateAuth(TestDataGenerator.createCognitoAuthResult());
      
      // Mock DynamoDB user query
      mockDynamoDBQuery([TEST_USERS.VALID_USER]);

      const signinData = {
        email: TEST_CREDENTIALS.VALID.email,
        password: TEST_CREDENTIALS.VALID.password
      };

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/auth/signin',
        body: JSON.stringify(signinData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.message).toBe('Sign in successful');
      expect(body.tokens).toBeDefined();
      expect(body.tokens.accessToken).toBeDefined();
      expect(body.tokens.refreshToken).toBeDefined();
      expect(body.tokens.idToken).toBeDefined();
      expect(body.user).toBeDefined();
      expect(body.user.email).toBe(TEST_USERS.VALID_USER.email);
    });

    it('should return 400 when credentials are missing', async () => {
      const testCases = [
        { email: '<EMAIL>' }, // Missing password
        { password: 'Password123!' }, // Missing email
        {} // All missing
      ];

      for (const testCase of testCases) {
        const event = TestDataGenerator.createAPIGatewayEvent({
          httpMethod: 'POST',
          path: '/auth/signin',
          body: JSON.stringify(testCase)
        });
        const context = TestDataGenerator.createLambdaContext();

        const result = await handler(event, context);

        expect(result.statusCode).toBe(400);
        const body = JSON.parse(result.body);
        expect(body.error).toBe('Email and password are required');
      }
    });

    it('should return 401 when authentication fails', async () => {
      // Mock Cognito auth error
      require('../utils/aws-mocks').mockCognitoIdentityServiceProvider.adminInitiateAuth.mockImplementation(() => ({
        promise: () => Promise.reject(new Error('Incorrect username or password'))
      }));

      const signinData = {
        email: TEST_CREDENTIALS.INVALID_PASSWORD.email,
        password: TEST_CREDENTIALS.INVALID_PASSWORD.password
      };

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/auth/signin',
        body: JSON.stringify(signinData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(401);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Authentication failed');
      expect(body.details).toBe('Incorrect username or password');
    });

    it('should return 404 when user is not found in database', async () => {
      // Mock Cognito auth response
      mockCognitoAdminInitiateAuth(TestDataGenerator.createCognitoAuthResult());
      
      // Mock empty DynamoDB query result
      mockDynamoDBQuery([]);

      const signinData = {
        email: '<EMAIL>',
        password: 'Password123!'
      };

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/auth/signin',
        body: JSON.stringify(signinData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(404);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('User not found');
    });
  });

  describe('POST /auth/refresh', () => {
    it('should refresh tokens successfully', async () => {
      // Mock Cognito auth response
      mockCognitoAdminInitiateAuth(TestDataGenerator.createCognitoAuthResult());

      const refreshData = {
        refreshToken: 'valid-refresh-token'
      };

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/auth/refresh',
        body: JSON.stringify(refreshData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.message).toBe('Token refreshed successfully');
      expect(body.tokens).toBeDefined();
      expect(body.tokens.accessToken).toBeDefined();
      expect(body.tokens.idToken).toBeDefined();
    });

    it('should return 400 when refresh token is missing', async () => {
      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/auth/refresh',
        body: JSON.stringify({})
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Refresh token is required');
    });

    it('should return 401 when refresh token is invalid', async () => {
      // Mock Cognito auth error
      require('../utils/aws-mocks').mockCognitoIdentityServiceProvider.adminInitiateAuth.mockImplementation(() => ({
        promise: () => Promise.reject(new Error('Invalid refresh token'))
      }));

      const refreshData = {
        refreshToken: 'invalid-refresh-token'
      };

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/auth/refresh',
        body: JSON.stringify(refreshData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(401);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Token refresh failed');
      expect(body.details).toBe('Invalid refresh token');
    });
  });

  describe('Error Handling', () => {
    it('should return 404 for unknown endpoints', async () => {
      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/auth/unknown'
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(404);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Not found');
    });

    it('should handle invalid JSON in request body', async () => {
      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/auth/signup',
        body: 'invalid-json'
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Invalid JSON in request body');
    });

    it('should include CORS headers in all responses', async () => {
      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/auth/unknown'
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.headers).toBeDefined();
      expect(result.headers['Access-Control-Allow-Origin']).toBe('*');
      expect(result.headers['Access-Control-Allow-Headers']).toBeDefined();
      expect(result.headers['Access-Control-Allow-Methods']).toBeDefined();
    });
  });
});
