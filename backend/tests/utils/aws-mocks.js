/**
 * AWS service mocks for testing
 */

// Mock AWS SDK
const AWS = require('aws-sdk');

// Mock DynamoDB
const mockDynamoDBDocumentClient = {
  get: jest.fn(),
  put: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  query: jest.fn(),
  scan: jest.fn()
};

// Mock S3
const mockS3 = {
  getSignedUrl: jest.fn(),
  getObject: jest.fn(),
  putObject: jest.fn(),
  deleteObject: jest.fn(),
  listObjects: jest.fn()
};

// Mock Cognito
const mockCognitoIdentityServiceProvider = {
  adminCreateUser: jest.fn(),
  adminSetUserPassword: jest.fn(),
  adminInitiateAuth: jest.fn(),
  adminGetUser: jest.fn(),
  adminUpdateUserAttributes: jest.fn(),
  adminDeleteUser: jest.fn(),
  listUsers: jest.fn()
};

// Setup AWS SDK mocks
jest.mock('aws-sdk', () => {
  return {
    DynamoDB: {
      DocumentClient: jest.fn(() => mockDynamoDBDocumentClient)
    },
    S3: jest.fn(() => mockS3),
    CognitoIdentityServiceProvider: jest.fn(() => mockCognitoIdentityServiceProvider)
  };
});

// Helper to reset all mocks
const resetAllMocks = () => {
  // Reset DynamoDB mocks
  mockDynamoDBDocumentClient.get.mockReset();
  mockDynamoDBDocumentClient.put.mockReset();
  mockDynamoDBDocumentClient.update.mockReset();
  mockDynamoDBDocumentClient.delete.mockReset();
  mockDynamoDBDocumentClient.query.mockReset();
  mockDynamoDBDocumentClient.scan.mockReset();

  // Reset S3 mocks
  mockS3.getSignedUrl.mockReset();
  mockS3.getObject.mockReset();
  mockS3.putObject.mockReset();
  mockS3.deleteObject.mockReset();
  mockS3.listObjects.mockReset();

  // Reset Cognito mocks
  mockCognitoIdentityServiceProvider.adminCreateUser.mockReset();
  mockCognitoIdentityServiceProvider.adminSetUserPassword.mockReset();
  mockCognitoIdentityServiceProvider.adminInitiateAuth.mockReset();
  mockCognitoIdentityServiceProvider.adminGetUser.mockReset();
  mockCognitoIdentityServiceProvider.adminUpdateUserAttributes.mockReset();
  mockCognitoIdentityServiceProvider.adminDeleteUser.mockReset();
  mockCognitoIdentityServiceProvider.listUsers.mockReset();
};

// Helper to mock successful DynamoDB get
const mockDynamoDBGet = (item) => {
  mockDynamoDBDocumentClient.get.mockImplementation(() => ({
    promise: () => Promise.resolve({ Item: item })
  }));
};

// Helper to mock successful DynamoDB put
const mockDynamoDBPut = () => {
  mockDynamoDBDocumentClient.put.mockImplementation(() => ({
    promise: () => Promise.resolve({})
  }));
};

// Helper to mock successful DynamoDB update
const mockDynamoDBUpdate = (attributes) => {
  mockDynamoDBDocumentClient.update.mockImplementation(() => ({
    promise: () => Promise.resolve({ Attributes: attributes })
  }));
};

// Helper to mock successful DynamoDB query
const mockDynamoDBQuery = (items) => {
  mockDynamoDBDocumentClient.query.mockImplementation(() => ({
    promise: () => Promise.resolve({ Items: items })
  }));
};

// Helper to mock successful DynamoDB scan
const mockDynamoDBScan = (items) => {
  mockDynamoDBDocumentClient.scan.mockImplementation(() => ({
    promise: () => Promise.resolve({ Items: items })
  }));
};

// Helper to mock successful Cognito adminInitiateAuth
const mockCognitoAdminInitiateAuth = (authResult) => {
  mockCognitoIdentityServiceProvider.adminInitiateAuth.mockImplementation(() => ({
    promise: () => Promise.resolve(authResult)
  }));
};

// Helper to mock successful Cognito adminCreateUser
const mockCognitoAdminCreateUser = (user) => {
  mockCognitoIdentityServiceProvider.adminCreateUser.mockImplementation(() => ({
    promise: () => Promise.resolve(user)
  }));
};

// Helper to mock successful S3 getSignedUrl
const mockS3GetSignedUrl = (url) => {
  mockS3.getSignedUrl.mockImplementation(() => url);
};

module.exports = {
  mockDynamoDBDocumentClient,
  mockS3,
  mockCognitoIdentityServiceProvider,
  resetAllMocks,
  mockDynamoDBGet,
  mockDynamoDBPut,
  mockDynamoDBUpdate,
  mockDynamoDBQuery,
  mockDynamoDBScan,
  mockCognitoAdminInitiateAuth,
  mockCognitoAdminCreateUser,
  mockS3GetSignedUrl
};
