/**
 * Integration tests for the GameFlex SAM Backend API
 * These tests verify that all services work together correctly
 */

const { TestDataGenerator, TEST_USERS, TEST_CREDENTIALS } = require('../utils/test-data');
const { 
  resetAllMocks, 
  mockDynamoDBGet, 
  mockDynamoDBPut, 
  mockDynamoDBQuery,
  mockDynamoDBScan,
  mockCognitoAdminCreateUser,
  mockCognitoAdminInitiateAuth,
  mockS3GetSignedUrl
} = require('../utils/aws-mocks');

// Import all handlers
const authHandler = require('../../src/auth/index').handler;
const postsHandler = require('../../src/posts/index').handler;
const mediaHandler = require('../../src/media/index').handler;
const usersHandler = require('../../src/users/index').handler;
const healthHandler = require('../../src/health/index').handler;

describe('API Integration Tests', () => {
  beforeEach(() => {
    resetAllMocks();
  });

  describe('Health Check Integration', () => {
    it('should return healthy status for all services', async () => {
      // Mock successful DynamoDB connection
      mockDynamoDBScan([]);

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/health'
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await healthHandler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.status).toBe('healthy');
      expect(body.services.database).toBe('healthy');
      expect(body.services.api).toBe('healthy');
    });
  });

  describe('Authentication Flow Integration', () => {
    it('should complete full signup and signin flow', async () => {
      // Step 1: Sign up a new user
      mockCognitoAdminCreateUser(TestDataGenerator.createCognitoUser());
      mockDynamoDBPut();

      const signupData = {
        email: '<EMAIL>',
        password: 'IntegrationTest123!',
        username: 'integrationuser',
        firstName: 'Integration',
        lastName: 'Test'
      };

      const signupEvent = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/auth/signup',
        body: JSON.stringify(signupData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const signupResult = await authHandler(signupEvent, context);
      expect(signupResult.statusCode).toBe(201);

      // Step 2: Sign in with the new user
      mockCognitoAdminInitiateAuth(TestDataGenerator.createCognitoAuthResult());
      mockDynamoDBQuery([TestDataGenerator.createUser({
        email: signupData.email,
        username: signupData.username
      })]);

      const signinData = {
        email: signupData.email,
        password: signupData.password
      };

      const signinEvent = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/auth/signin',
        body: JSON.stringify(signinData)
      });

      const signinResult = await authHandler(signinEvent, context);
      expect(signinResult.statusCode).toBe(200);

      const signinBody = JSON.parse(signinResult.body);
      expect(signinBody.tokens).toBeDefined();
      expect(signinBody.user.email).toBe(signupData.email);
    });

    it('should refresh tokens successfully', async () => {
      mockCognitoAdminInitiateAuth(TestDataGenerator.createCognitoAuthResult());

      const refreshEvent = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/auth/refresh',
        body: JSON.stringify({ refreshToken: 'valid-refresh-token' })
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await authHandler(refreshEvent, context);
      expect(result.statusCode).toBe(200);

      const body = JSON.parse(result.body);
      expect(body.tokens.accessToken).toBeDefined();
      expect(body.tokens.idToken).toBeDefined();
    });
  });

  describe('Posts and Media Integration', () => {
    it('should create post with media upload flow', async () => {
      const userId = TEST_USERS.VALID_USER.id;

      // Step 1: Upload media
      mockDynamoDBPut();
      mockS3GetSignedUrl('https://test-upload-url.com');

      const mediaUploadData = {
        fileName: 'test-post-image.jpg',
        fileType: 'image/jpeg',
        fileSize: 1024000,
        mediaType: 'image',
        userId: userId
      };

      const mediaEvent = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/media/upload',
        body: JSON.stringify(mediaUploadData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const mediaResult = await mediaHandler(mediaEvent, context);
      expect(mediaResult.statusCode).toBe(200);

      const mediaBody = JSON.parse(mediaResult.body);
      const mediaId = mediaBody.mediaId;
      const mediaUrl = mediaBody.media.url;

      // Step 2: Create post with media
      mockDynamoDBPut();

      const postData = {
        title: 'Integration Test Post',
        content: 'This post includes uploaded media',
        userId: userId,
        mediaUrl: mediaUrl
      };

      const postEvent = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/posts',
        body: JSON.stringify(postData)
      });

      const postResult = await postsHandler(postEvent, context);
      expect(postResult.statusCode).toBe(201);

      const postBody = JSON.parse(postResult.body);
      expect(postBody.post.mediaUrl).toBe(mediaUrl);
      expect(postBody.post.title).toBe(postData.title);
    });

    it('should complete post interaction flow (create, like, unlike)', async () => {
      const userId = TEST_USERS.VALID_USER.id;

      // Step 1: Create a post
      mockDynamoDBPut();

      const postData = {
        title: 'Likeable Post',
        content: 'This post will be liked and unliked',
        userId: userId
      };

      const createEvent = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/posts',
        body: JSON.stringify(postData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const createResult = await postsHandler(createEvent, context);
      expect(createResult.statusCode).toBe(201);

      const createBody = JSON.parse(createResult.body);
      const postId = createBody.post.id;

      // Step 2: Like the post
      mockDynamoDBGet(null); // No existing like
      mockDynamoDBPut(); // Add like
      require('../utils/aws-mocks').mockDynamoDBDocumentClient.update.mockImplementation(() => ({
        promise: () => Promise.resolve({})
      }));

      const likeEvent = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: `/posts/${postId}/like`,
        pathParameters: { id: postId },
        body: JSON.stringify({ userId: userId })
      });

      const likeResult = await postsHandler(likeEvent, context);
      expect(likeResult.statusCode).toBe(200);

      // Step 3: Unlike the post
      require('../utils/aws-mocks').mockDynamoDBDocumentClient.delete.mockImplementation(() => ({
        promise: () => Promise.resolve({})
      }));

      const unlikeEvent = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'DELETE',
        path: `/posts/${postId}/like`,
        pathParameters: { id: postId },
        body: JSON.stringify({ userId: userId })
      });

      const unlikeResult = await postsHandler(unlikeEvent, context);
      expect(unlikeResult.statusCode).toBe(200);
    });
  });

  describe('User Profile and Social Features Integration', () => {
    it('should complete user profile and follow flow', async () => {
      const user1Id = TEST_USERS.VALID_USER.id;
      const user2Id = TEST_USERS.ADMIN_USER.id;

      // Step 1: Update user profile
      require('../utils/aws-mocks').mockDynamoDBDocumentClient.update.mockImplementation(() => ({
        promise: () => Promise.resolve({})
      }));

      const profileData = {
        userId: user1Id,
        firstName: 'Updated',
        lastName: 'User',
        bio: 'Integration test user profile',
        location: 'Test City'
      };

      const profileEvent = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'PUT',
        path: '/users/profile',
        body: JSON.stringify(profileData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const profileResult = await usersHandler(profileEvent, context);
      expect(profileResult.statusCode).toBe(200);

      // Step 2: Follow another user
      mockDynamoDBGet(null); // No existing follow
      mockDynamoDBPut(); // Add follow
      require('../utils/aws-mocks').mockDynamoDBDocumentClient.update.mockImplementation(() => ({
        promise: () => Promise.resolve({})
      }));

      const followEvent = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: `/users/${user2Id}/follow`,
        pathParameters: { id: user2Id },
        body: JSON.stringify({ userId: user1Id })
      });

      const followResult = await usersHandler(followEvent, context);
      expect(followResult.statusCode).toBe(200);

      // Step 3: Get user profile to verify follow count
      require('../utils/aws-mocks').mockDynamoDBDocumentClient.get
        .mockImplementationOnce(() => ({
          promise: () => Promise.resolve({ Item: TEST_USERS.VALID_USER })
        }))
        .mockImplementationOnce(() => ({
          promise: () => Promise.resolve({ 
            Item: TestDataGenerator.createUserProfile(user1Id, { followingCount: 1 })
          })
        }));

      const getProfileEvent = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/users/profile',
        queryStringParameters: { userId: user1Id }
      });

      const getProfileResult = await usersHandler(getProfileEvent, context);
      expect(getProfileResult.statusCode).toBe(200);

      const getProfileBody = JSON.parse(getProfileResult.body);
      expect(getProfileBody.user.followingCount).toBe(1);
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle cascading errors gracefully', async () => {
      // Test that when one service fails, others still work
      
      // Make DynamoDB fail for posts
      require('../utils/aws-mocks').mockDynamoDBDocumentClient.scan.mockImplementation(() => ({
        promise: () => Promise.reject(new Error('DynamoDB connection failed'))
      }));

      const postsEvent = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/posts'
      });
      const context = TestDataGenerator.createLambdaContext();

      const postsResult = await postsHandler(postsEvent, context);
      expect(postsResult.statusCode).toBe(500);

      // But health check should still work (with database marked as unhealthy)
      const healthEvent = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/health'
      });

      const healthResult = await healthHandler(healthEvent, context);
      expect(healthResult.statusCode).toBe(200);

      const healthBody = JSON.parse(healthResult.body);
      expect(healthBody.services.database).toBe('unhealthy');
      expect(healthBody.services.api).toBe('healthy');
    });
  });

  describe('CORS Integration', () => {
    it('should include CORS headers in all service responses', async () => {
      const services = [
        { handler: authHandler, path: '/auth/signin', method: 'POST' },
        { handler: postsHandler, path: '/posts', method: 'GET' },
        { handler: mediaHandler, path: '/media/upload', method: 'POST' },
        { handler: usersHandler, path: '/users/profile', method: 'GET' },
        { handler: healthHandler, path: '/health', method: 'GET' }
      ];

      for (const service of services) {
        const event = TestDataGenerator.createAPIGatewayEvent({
          httpMethod: service.method,
          path: service.path,
          body: service.method === 'POST' ? JSON.stringify({}) : null
        });
        const context = TestDataGenerator.createLambdaContext();

        const result = await service.handler(event, context);

        expect(result.headers).toBeDefined();
        expect(result.headers['Access-Control-Allow-Origin']).toBe('*');
        expect(result.headers['Access-Control-Allow-Headers']).toBeDefined();
        expect(result.headers['Access-Control-Allow-Methods']).toBeDefined();
      }
    });
  });
});
