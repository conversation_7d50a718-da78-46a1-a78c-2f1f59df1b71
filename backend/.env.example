# GameFlex SAM Backend Environment Variables
# Copy this file to .env and modify as needed

# Environment settings
ENVIRONMENT=development
PROJECT_NAME=gameflex

# AWS Configuration
AWS_REGION=us-east-1
AWS_ENDPOINT_URL=http://localhost:4566
AWS_ACCESS_KEY_ID=test
AWS_SECRET_ACCESS_KEY=test

# LocalStack settings
LOCALSTACK_HOST=localhost
LOCALSTACK_PORT=4566

# API Gateway settings
API_PORT=3000

# Debug settings
DEBUG=1
