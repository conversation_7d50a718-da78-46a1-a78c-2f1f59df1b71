#!/bin/bash

# GameFlex SAM Backend Startup Script
# This script starts the AWS SAM local environment and initializes services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[GAMEFLEX]${NC} $1"
}

# Load environment variables from .env file
load_env_file() {
    if [ -f ".env" ]; then
        print_status "Loading environment variables from .env file..."
        # Export variables from .env file, ignoring comments and empty lines
        export $(grep -v '^#' .env | grep -v '^$' | xargs)
    else
        print_warning ".env file not found. Using default values."
    fi
}

# Check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    print_status "Docker is running"
}

# Check if required ports are available
check_ports() {
    local ports=("3000" "4566" "4571")
    for port in "${ports[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            print_warning "Port $port is already in use"
            read -p "Do you want to continue anyway? (y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                print_error "Startup cancelled"
                exit 1
            fi
        fi
    done
    print_status "Port check completed"
}

# Check if SAM CLI is installed
check_sam_cli() {
    if ! command -v sam &> /dev/null; then
        print_error "AWS SAM CLI is not installed. Please install it first."
        print_status "Installation guide: https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/install-sam-cli.html"
        exit 1
    fi
    print_status "AWS SAM CLI is installed"
}

# Start LocalStack
start_localstack() {
    print_status "Starting LocalStack..."
    
    # Create docker-compose.yml for LocalStack if it doesn't exist
    if [ ! -f "docker-compose.yml" ]; then
        cat > docker-compose.yml << EOF
version: '3.8'
services:
  localstack:
    container_name: gameflex-localstack
    image: localstack/localstack:latest
    ports:
      - "4566:4566"
      - "4571:4571"
    environment:
      - SERVICES=s3,dynamodb,cognito,lambda,apigateway
      - DEBUG=1
      - DATA_DIR=/tmp/localstack/data
      - DOCKER_HOST=unix:///var/run/docker.sock
      - AWS_DEFAULT_REGION=us-east-1
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
    volumes:
      - "./volume:/var/lib/localstack"
      - "/var/run/docker.sock:/var/run/docker.sock"
EOF
        print_status "Created docker-compose.yml for LocalStack"
    fi
    
    # Start LocalStack
    docker-compose up -d
    
    # Wait for LocalStack to be ready
    print_status "Waiting for LocalStack to be ready..."
    timeout=120
    counter=0
    while [ $counter -lt $timeout ]; do
        if curl -s http://localhost:4566/_localstack/health > /dev/null 2>&1; then
            print_status "LocalStack is ready"
            break
        fi
        sleep 2
        counter=$((counter + 2))
        if [ $counter -ge $timeout ]; then
            print_error "LocalStack failed to start within $timeout seconds"
            docker-compose logs localstack
            exit 1
        fi
    done
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    # Make scripts executable
    chmod +x scripts/*.sh
    
    # Run the dependency installation script
    ./scripts/install-dependencies.sh
}

# Initialize AWS services
initialize_aws_services() {
    print_status "Initializing AWS services..."
    
    # Run the AWS services initialization script
    ./scripts/init-aws-services.sh
}

# Start SAM local API
start_sam_local() {
    print_status "Starting SAM local API..."
    
    # Set environment variables for SAM local
    export AWS_ENDPOINT_URL=http://localhost:4566
    export AWS_REGION=us-east-1
    export AWS_ACCESS_KEY_ID=test
    export AWS_SECRET_ACCESS_KEY=test
    
    # Start SAM local API
    sam local start-api \
        --docker-network host \
        --parameter-overrides "Environment=development ProjectName=gameflex" \
        --warm-containers EAGER \
        --port 3000 &
    
    # Save the PID to kill it later
    echo $! > .sam_pid
    
    print_status "SAM local API started on http://localhost:3000"
}

# Display service information
display_info() {
    print_header "GameFlex SAM Backend is now running!"
    echo
    print_status "Service URLs:"
    echo "  🌐 SAM Local API: http://localhost:3000"
    echo "  🌐 LocalStack Dashboard: http://localhost:4566/_localstack/health"
    echo "  📦 S3 Console: http://localhost:4566/_aws/s3"
    echo "  🔐 Cognito Console: http://localhost:4566/_aws/cognito"
    echo
    print_status "Development Credentials:"
    echo "  📧 Developer: <EMAIL> / DevPassword123!"
    echo "  👑 Admin: <EMAIL> / AdminPassword123!"
    echo
    print_status "Useful Commands:"
    echo "  📊 Check status: docker-compose ps"
    echo "  📋 View logs: docker-compose logs -f"
    echo "  🛑 Stop services: ./stop.sh"
    echo
    print_warning "This is a development environment. Do not use in production!"
}

# Cleanup function
cleanup() {
    print_status "Cleaning up..."
    
    # Kill SAM local process
    if [ -f .sam_pid ]; then
        kill $(cat .sam_pid) 2>/dev/null || true
        rm .sam_pid
    fi
    
    # Stop LocalStack
    docker-compose down
    
    print_status "Cleanup completed"
}

# Trap SIGINT and SIGTERM
trap cleanup SIGINT SIGTERM

# Main execution
main() {
    print_header "GameFlex SAM Backend Startup"
    echo
    
    # Load environment variables from .env file
    load_env_file
    
    check_docker
    check_ports
    check_sam_cli
    
    # Create necessary directories
    mkdir -p volume
    
    start_localstack
    install_dependencies
    initialize_aws_services
    start_sam_local
    display_info
    
    print_status "Startup completed successfully!"
    
    # Keep the script running to handle signals
    echo
    print_status "Press Ctrl+C to stop the services"
    while true; do
        sleep 1
    done
}

# Run main function
main "$@"
