{"name": "gameflex-sam-backend", "version": "1.0.0", "description": "GameFlex Backend using AWS SAM for local development and deployment", "main": "template.yaml", "scripts": {"start": "./start.sh", "stop": "./stop.sh", "install-deps": "./scripts/install-dependencies.sh", "build": "sam build", "deploy": "sam deploy --guided", "local": "sam local start-api --parameter-overrides 'Environment=development ProjectName=gameflex' --warm-containers EAGER --port 3000", "validate": "sam validate", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest tests/unit", "test:integration": "jest tests/integration", "test:e2e": "jest tests/e2e", "test:run": "./scripts/run-tests.sh"}, "keywords": ["aws", "sam", "serverless", "lambda", "api-gateway", "dynamodb", "cognito", "s3", "gameflex"], "author": "GameFlex Team", "license": "MIT", "devDependencies": {"@types/aws-lambda": "^8.10.150", "@types/jest": "^29.5.12", "@types/node": "^20.19.7", "jest": "^29.7.0", "babel-jest": "^29.7.0", "@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0", "aws-sdk-client-mock": "^3.0.0", "aws-sdk-client-mock-jest": "^3.0.0", "supertest": "^6.3.3"}, "dependencies": {"aws-sdk": "^2.1499.0", "uuid": "^9.0.1"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/gameflex/gameflex-backend"}, "bugs": {"url": "https://github.com/gameflex/gameflex-backend/issues"}, "homepage": "https://github.com/gameflex/gameflex-backend#readme"}