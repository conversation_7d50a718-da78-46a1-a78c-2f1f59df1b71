{"name": "gameflex-sam-backend", "version": "1.0.0", "description": "GameFlex Backend using AWS SAM for local development and deployment", "main": "template.yaml", "scripts": {"start": "./start.sh", "stop": "./stop.sh", "install-deps": "./scripts/install-dependencies.sh", "build": "sam build", "deploy": "sam deploy --guided", "local": "sam local start-api --parameter-overrides 'Environment=development ProjectName=gameflex' --warm-containers EAGER --port 3000", "validate": "sam validate", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["aws", "sam", "serverless", "lambda", "api-gateway", "dynamodb", "cognito", "s3", "gameflex"], "author": "GameFlex Team", "license": "MIT", "devDependencies": {"aws-sam-cli": "^1.0.0"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/gameflex/gameflex-backend"}, "bugs": {"url": "https://github.com/gameflex/gameflex-backend/issues"}, "homepage": "https://github.com/gameflex/gameflex-backend#readme"}