const AWS = require('aws-sdk');

// Configure AWS SDK for LocalStack
const awsConfig = {
    region: process.env.AWS_REGION || 'us-east-1',
    endpoint: process.env.AWS_ENDPOINT_URL || 'http://localhost:4566',
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || 'test',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || 'test'
};

const dynamodb = new AWS.DynamoDB.DocumentClient(awsConfig);

// Helper function to create response
const createResponse = (statusCode, body) => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});

// Health check function
const healthCheck = async () => {
    try {
        const timestamp = new Date().toISOString();
        const environment = process.env.ENVIRONMENT || 'development';
        
        // Test DynamoDB connection
        let dbStatus = 'healthy';
        try {
            await dynamodb.scan({
                TableName: process.env.USERS_TABLE || 'gameflex-development-Users',
                Limit: 1
            }).promise();
        } catch (error) {
            dbStatus = 'unhealthy';
            console.error('DynamoDB health check failed:', error);
        }

        const healthData = {
            status: 'healthy',
            timestamp,
            environment,
            version: '1.0.0',
            services: {
                database: dbStatus,
                api: 'healthy'
            },
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            environment_variables: {
                USER_POOL_ID: process.env.USER_POOL_ID ? 'set' : 'not set',
                USER_POOL_CLIENT_ID: process.env.USER_POOL_CLIENT_ID ? 'set' : 'not set',
                USERS_TABLE: process.env.USERS_TABLE ? 'set' : 'not set',
                POSTS_TABLE: process.env.POSTS_TABLE ? 'set' : 'not set',
                MEDIA_BUCKET: process.env.MEDIA_BUCKET ? 'set' : 'not set'
            }
        };

        return createResponse(200, healthData);

    } catch (error) {
        console.error('Health check error:', error);
        return createResponse(500, {
            status: 'unhealthy',
            timestamp: new Date().toISOString(),
            error: error.message
        });
    }
};

// Main handler
exports.handler = async (event) => {
    console.log('Health check event:', JSON.stringify(event, null, 2));

    const { httpMethod, path } = event;

    if (httpMethod === 'GET' && path === '/health') {
        return await healthCheck();
    } else {
        return createResponse(404, { error: 'Not found' });
    }
};
