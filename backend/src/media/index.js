const AWS = require('aws-sdk');
const { v4: uuidv4 } = require('uuid');

// Configure AWS SDK for LocalStack
const awsConfig = {
    region: process.env.AWS_REGION || 'us-east-1',
    endpoint: process.env.AWS_ENDPOINT_URL || 'http://localhost:4566',
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || 'test',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || 'test'
};

const dynamodb = new AWS.DynamoDB.DocumentClient(awsConfig);
const s3 = new AWS.S3(awsConfig);

const MEDIA_TABLE = process.env.MEDIA_TABLE;
const MEDIA_BUCKET = process.env.MEDIA_BUCKET;
const AVATARS_BUCKET = process.env.AVATARS_BUCKET;
const TEMP_BUCKET = process.env.TEMP_BUCKET;

// Helper function to create response
const createResponse = (statusCode, body) => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});

// Upload media
const uploadMedia = async (event) => {
    try {
        const { fileName, fileType, fileSize, mediaType, userId } = JSON.parse(event.body);

        if (!fileName || !fileType || !userId) {
            return createResponse(400, { error: 'fileName, fileType, and userId are required' });
        }

        const mediaId = uuidv4();
        const fileExtension = fileName.split('.').pop();
        const s3Key = `${userId}/${mediaId}.${fileExtension}`;
        
        // Determine bucket based on media type
        let bucketName = MEDIA_BUCKET;
        if (mediaType === 'avatar') {
            bucketName = AVATARS_BUCKET;
        }

        // Generate presigned URL for upload
        const presignedUrl = s3.getSignedUrl('putObject', {
            Bucket: bucketName,
            Key: s3Key,
            ContentType: fileType,
            Expires: 300 // 5 minutes
        });

        // Create media record
        const mediaRecord = {
            id: mediaId,
            fileName,
            fileType,
            fileSize: fileSize || 0,
            mediaType: mediaType || 'image',
            userId,
            s3Key,
            bucketName,
            url: `http://localhost:4566/${bucketName}/${s3Key}`,
            status: 'pending',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };

        await dynamodb.put({
            TableName: MEDIA_TABLE,
            Item: mediaRecord
        }).promise();

        return createResponse(200, {
            message: 'Upload URL generated successfully',
            mediaId,
            uploadUrl: presignedUrl,
            media: mediaRecord
        });

    } catch (error) {
        console.error('UploadMedia error:', error);
        return createResponse(500, { error: 'Failed to generate upload URL', details: error.message });
    }
};

// Get media
const getMedia = async (event) => {
    try {
        const { id } = event.pathParameters;

        const result = await dynamodb.get({
            TableName: MEDIA_TABLE,
            Key: { id }
        }).promise();

        if (!result.Item) {
            return createResponse(404, { error: 'Media not found' });
        }

        const media = result.Item;

        // Generate presigned URL for download if needed
        if (media.status === 'uploaded') {
            const downloadUrl = s3.getSignedUrl('getObject', {
                Bucket: media.bucketName,
                Key: media.s3Key,
                Expires: 3600 // 1 hour
            });

            media.downloadUrl = downloadUrl;
        }

        return createResponse(200, { media });

    } catch (error) {
        console.error('GetMedia error:', error);
        return createResponse(500, { error: 'Failed to get media', details: error.message });
    }
};

// Delete media
const deleteMedia = async (event) => {
    try {
        const { id } = event.pathParameters;

        // Get media record
        const result = await dynamodb.get({
            TableName: MEDIA_TABLE,
            Key: { id }
        }).promise();

        if (!result.Item) {
            return createResponse(404, { error: 'Media not found' });
        }

        const media = result.Item;

        // Delete from S3
        if (media.s3Key && media.bucketName) {
            await s3.deleteObject({
                Bucket: media.bucketName,
                Key: media.s3Key
            }).promise();
        }

        // Delete from DynamoDB
        await dynamodb.delete({
            TableName: MEDIA_TABLE,
            Key: { id }
        }).promise();

        return createResponse(200, { message: 'Media deleted successfully' });

    } catch (error) {
        console.error('DeleteMedia error:', error);
        return createResponse(500, { error: 'Failed to delete media', details: error.message });
    }
};

// Update media status (called after successful upload)
const updateMediaStatus = async (event) => {
    try {
        const { id } = event.pathParameters;
        const { status } = JSON.parse(event.body);

        if (!status) {
            return createResponse(400, { error: 'Status is required' });
        }

        const result = await dynamodb.update({
            TableName: MEDIA_TABLE,
            Key: { id },
            UpdateExpression: 'SET #status = :status, updated_at = :updated_at',
            ExpressionAttributeNames: {
                '#status': 'status'
            },
            ExpressionAttributeValues: {
                ':status': status,
                ':updated_at': new Date().toISOString()
            },
            ReturnValues: 'ALL_NEW'
        }).promise();

        return createResponse(200, {
            message: 'Media status updated successfully',
            media: result.Attributes
        });

    } catch (error) {
        console.error('UpdateMediaStatus error:', error);
        return createResponse(500, { error: 'Failed to update media status', details: error.message });
    }
};

// Main handler
exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, pathParameters } = event;

    try {
        if (httpMethod === 'POST' && path === '/media/upload') {
            return await uploadMedia(event);
        } else if (httpMethod === 'GET' && pathParameters && pathParameters.id) {
            return await getMedia(event);
        } else if (httpMethod === 'DELETE' && pathParameters && pathParameters.id) {
            return await deleteMedia(event);
        } else if (httpMethod === 'PUT' && pathParameters && pathParameters.id) {
            return await updateMediaStatus(event);
        } else {
            return createResponse(404, { error: 'Not found' });
        }
    } catch (error) {
        console.error('Handler error:', error);
        return createResponse(500, { error: 'Internal server error', details: error.message });
    }
};
